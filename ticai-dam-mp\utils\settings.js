const config = {
  // api_host: 'https://dam.ziyuan0898.com/port-api', //api地址
  // attach_host: 'https://dam.ziyuan0898.com/attach', //附件地址
  // web_host: 'https://dam.ziyuan0898.com', // web地址
  // api_id: '002', // 接口ID
  // api_secret: '3930FED1F80ACDB42A60C8D30FFC40BD', // 接口密钥
  // amap_key: '9ee4264d24915bb36826bcc41b2cc9ff' // 高德Key

  // api_host: 'http://*************:30007/port-api', //api地址
  // attach_host: 'http://*************:30007/attach', //附件地址
  // web_host: 'http://*************:30007', // web地址
  // api_id: '002', // 接口ID
  // api_secret: '3930FED1F80ACDB42A60C8D30FFC40BD', // 接口密钥
  // amap_key: 'cc354a244d3f08c17c6eab6373bdd7bb', // 高德Key

  // api_host: "http://localhost:20002/port-api", //api地址（本地开发）
  // attach_host: "http://localhost:20002/attach", //附件地址（本地开发）
  // web_host: "http://localhost:20002", // web地址（本地开发）

  // api_host: "https://tcsb.hnvlts.com:10443/port-api", //api地址（正式环境）
  // attach_host: "https://tcsb.hnvlts.com:10443/attach", //附件地址（正式环境）
  // web_host: "https://tcsb.hnvlts.com:10443", // web地址（正式环境）

  // 测试环境
  api_host: "https://ticai.staging.hntsz.com/port-api", //api地址（测试环境）
  attach_host: "https://ticai.staging.hntsz.com/attach", //附件地址（测试环境）
  web_host: "https://ticai.staging.hntsz.com", // web地址（测试环境）

  api_id: "002", // 接口ID
  api_secret: "468727538DC034DA720193F178EDE4B5", // 接口密钥
  amap_key: "cc354a244d3f08c17c6eab6373bdd7bb", // 高德Key
};

// 判断是否为测试环境
config.isTestEnvironment = function () {
  return config.api_host && config.api_host.includes("staging");
};

module.exports = config;
